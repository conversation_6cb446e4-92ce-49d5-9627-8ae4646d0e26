"""
Pipeline management service for admin interface.

This module provides comprehensive pipeline management capabilities including:
- Pipeline execution control and monitoring
- Celery task management and monitoring
- Processing status tracking
- Pipeline configuration and validation
"""

import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from enum import Enum
from pathlib import Path

from celery import Celery
from celery.result import AsyncResult

import sys

# Add the src directory to the path
src_path = Path(__file__).parent.parent.parent
sys.path.insert(0, str(src_path))

from shared.config import get_settings
from shared.logging_config import get_logger
from shared.celery_app import celery_app
from shared.models import ProcessingStatus
from offline_pipeline.pipeline import OfflinePipeline
from offline_pipeline.tasks import (
    process_source_task,
    crawl_source_task,
    process_documents_task,
    generate_embeddings_task,
    store_vectors_task
)

logger = get_logger(__name__)


class PipelineMode(Enum):
    """Pipeline execution modes."""
    FULL = "full"
    INCREMENTAL = "incremental"
    REPROCESS = "reprocess"
    VALIDATE = "validate"


class TaskStatus(Enum):
    """Task status enumeration."""
    PENDING = "PENDING"
    STARTED = "STARTED"
    SUCCESS = "SUCCESS"
    FAILURE = "FAILURE"
    RETRY = "RETRY"
    REVOKED = "REVOKED"


class PipelineManager:
    """
    Administrative pipeline management service.
    
    Provides high-level pipeline control and monitoring capabilities
    for the admin interface.
    """
    
    def __init__(self):
        """Initialize the pipeline manager."""
        self.settings = get_settings()
        self.pipeline = OfflinePipeline()
        self._initialized = False
        
        # Task tracking
        self._active_tasks: Dict[str, Dict[str, Any]] = {}
        self._task_history: List[Dict[str, Any]] = []
        self._max_history_size = 1000
        
        logger.info("Pipeline manager initialized")
    
    async def initialize(self):
        """Initialize the pipeline manager."""
        try:
            await self.pipeline.initialize()
            self._initialized = True
            logger.info("Pipeline manager initialization completed")
        except Exception as e:
            logger.error(f"Pipeline manager initialization failed: {e}", exc_info=True)
            raise
    
    async def start_pipeline(
        self, 
        mode: PipelineMode = PipelineMode.FULL,
        source_ids: Optional[List[str]] = None,
        config_overrides: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Start pipeline execution."""
        try:
            if not self._initialized:
                await self.initialize()
            
            # Create task ID
            task_id = f"pipeline_{mode.value}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Prepare pipeline arguments
            pipeline_args = {
                "mode": mode.value,
                "source_ids": source_ids,
                "config_overrides": config_overrides or {}
            }
            
            # Start pipeline task
            if mode == PipelineMode.FULL:
                result = process_source_task.delay(**pipeline_args)
            elif mode == PipelineMode.INCREMENTAL:
                result = process_source_task.delay(**pipeline_args)
            elif mode == PipelineMode.REPROCESS:
                result = process_source_task.delay(**pipeline_args)
            else:  # VALIDATE
                result = self._validate_pipeline_config()
                return result
            
            # Track task
            task_info = {
                "task_id": result.id,
                "pipeline_task_id": task_id,
                "mode": mode.value,
                "source_ids": source_ids,
                "started_at": datetime.now(),
                "status": TaskStatus.PENDING.value,
                "progress": 0,
                "message": "Pipeline task queued"
            }
            
            self._active_tasks[result.id] = task_info
            
            logger.info(f"Started pipeline task: {task_id} (Celery ID: {result.id})")
            
            return {
                "task_id": result.id,
                "pipeline_task_id": task_id,
                "status": "started",
                "mode": mode.value,
                "source_ids": source_ids,
                "started_at": task_info["started_at"].isoformat()
            }
        except Exception as e:
            logger.error(f"Error starting pipeline: {e}", exc_info=True)
            raise
    
    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """Get status of a specific task."""
        try:
            # Check if it's in our active tasks
            if task_id in self._active_tasks:
                task_info = self._active_tasks[task_id]
                
                # Get Celery task result
                result = AsyncResult(task_id, app=celery_app)
                
                # Update status from Celery
                task_info["status"] = result.status
                
                if result.ready():
                    if result.successful():
                        task_info["status"] = TaskStatus.SUCCESS.value
                        task_info["result"] = result.result
                        task_info["completed_at"] = datetime.now()
                        task_info["progress"] = 100
                        task_info["message"] = "Task completed successfully"
                    else:
                        task_info["status"] = TaskStatus.FAILURE.value
                        task_info["error"] = str(result.info)
                        task_info["completed_at"] = datetime.now()
                        task_info["message"] = f"Task failed: {result.info}"
                
                return task_info
            else:
                # Try to get from Celery directly
                result = AsyncResult(task_id, app=celery_app)
                return {
                    "task_id": task_id,
                    "status": result.status,
                    "result": result.result if result.ready() else None,
                    "message": "Task not found in active tasks"
                }
        except Exception as e:
            logger.error(f"Error getting task status {task_id}: {e}", exc_info=True)
            return {
                "task_id": task_id,
                "status": "ERROR",
                "error": str(e),
                "message": "Error retrieving task status"
            }
    
    async def cancel_task(self, task_id: str) -> Dict[str, Any]:
        """Cancel a running task."""
        try:
            # Revoke the task
            celery_app.control.revoke(task_id, terminate=True)
            
            # Update our tracking
            if task_id in self._active_tasks:
                self._active_tasks[task_id]["status"] = TaskStatus.REVOKED.value
                self._active_tasks[task_id]["cancelled_at"] = datetime.now()
                self._active_tasks[task_id]["message"] = "Task cancelled by admin"
            
            logger.info(f"Cancelled task: {task_id}")
            
            return {
                "task_id": task_id,
                "status": "cancelled",
                "message": "Task cancelled successfully"
            }
        except Exception as e:
            logger.error(f"Error cancelling task {task_id}: {e}", exc_info=True)
            raise
    
    async def get_active_tasks(self) -> List[Dict[str, Any]]:
        """Get all currently active tasks."""
        try:
            active_tasks = []
            
            for task_id, task_info in self._active_tasks.items():
                # Update status from Celery
                result = AsyncResult(task_id, app=celery_app)
                task_info["status"] = result.status
                
                # Only include if still active
                if result.status in [TaskStatus.PENDING.value, TaskStatus.STARTED.value]:
                    active_tasks.append(task_info.copy())
                elif result.ready():
                    # Move to history
                    self._move_to_history(task_id, task_info)
            
            logger.info(f"Retrieved {len(active_tasks)} active tasks")
            return active_tasks
        except Exception as e:
            logger.error(f"Error getting active tasks: {e}", exc_info=True)
            return []
    
    async def get_task_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get task execution history."""
        try:
            # Return most recent tasks first
            history = sorted(
                self._task_history, 
                key=lambda x: x.get("started_at", datetime.min),
                reverse=True
            )
            
            return history[:limit]
        except Exception as e:
            logger.error(f"Error getting task history: {e}", exc_info=True)
            return []
    
    async def get_pipeline_statistics(self) -> Dict[str, Any]:
        """Get pipeline execution statistics."""
        try:
            # Count tasks by status
            status_counts = {}
            total_tasks = len(self._task_history) + len(self._active_tasks)
            
            for task_info in self._task_history:
                status = task_info.get("status", "unknown")
                status_counts[status] = status_counts.get(status, 0) + 1
            
            for task_info in self._active_tasks.values():
                status = task_info.get("status", "unknown")
                status_counts[status] = status_counts.get(status, 0) + 1
            
            # Calculate success rate
            successful_tasks = status_counts.get(TaskStatus.SUCCESS.value, 0)
            success_rate = (successful_tasks / total_tasks * 100) if total_tasks > 0 else 0
            
            # Get recent activity (last 24 hours)
            cutoff_time = datetime.now() - timedelta(hours=24)
            recent_tasks = [
                task for task in self._task_history
                if task.get("started_at", datetime.min) >= cutoff_time
            ]
            
            statistics = {
                "total_tasks": total_tasks,
                "active_tasks": len(self._active_tasks),
                "completed_tasks": len(self._task_history),
                "status_counts": status_counts,
                "success_rate_percent": round(success_rate, 2),
                "recent_tasks_24h": len(recent_tasks),
                "last_updated": datetime.now().isoformat()
            }
            
            logger.info("Generated pipeline statistics")
            return statistics
        except Exception as e:
            logger.error(f"Error getting pipeline statistics: {e}", exc_info=True)
            return {}
    
    async def get_worker_status(self) -> Dict[str, Any]:
        """Get Celery worker status."""
        try:
            inspect = celery_app.control.inspect()
            
            # Get worker information
            stats = await asyncio.get_event_loop().run_in_executor(
                None, inspect.stats
            )
            active = await asyncio.get_event_loop().run_in_executor(
                None, inspect.active
            )
            reserved = await asyncio.get_event_loop().run_in_executor(
                None, inspect.reserved
            )
            
            worker_info = {
                "total_workers": len(stats) if stats else 0,
                "active_workers": len(active) if active else 0,
                "workers": {},
                "total_active_tasks": 0,
                "total_reserved_tasks": 0
            }
            
            if stats:
                for worker_name, worker_stats in stats.items():
                    worker_active = active.get(worker_name, []) if active else []
                    worker_reserved = reserved.get(worker_name, []) if reserved else []
                    
                    worker_info["workers"][worker_name] = {
                        "status": "online",
                        "active_tasks": len(worker_active),
                        "reserved_tasks": len(worker_reserved),
                        "total_tasks": worker_stats.get("total", 0),
                        "pool_processes": worker_stats.get("pool", {}).get("processes", 0)
                    }
                    
                    worker_info["total_active_tasks"] += len(worker_active)
                    worker_info["total_reserved_tasks"] += len(worker_reserved)
            
            logger.info("Retrieved worker status")
            return worker_info
        except Exception as e:
            logger.error(f"Error getting worker status: {e}", exc_info=True)
            return {"error": str(e)}
    
    def _validate_pipeline_config(self) -> Dict[str, Any]:
        """Validate pipeline configuration."""
        try:
            # Basic configuration validation
            validation_results = {
                "valid": True,
                "errors": [],
                "warnings": [],
                "config_check": "passed"
            }
            
            # Check required settings
            required_settings = [
                "milvus_host", "milvus_port",
                "redis_host", "redis_port",
                "google_ai_api_key"
            ]
            
            for setting in required_settings:
                if not hasattr(self.settings, setting) or not getattr(self.settings, setting):
                    validation_results["errors"].append(f"Missing required setting: {setting}")
                    validation_results["valid"] = False
            
            if validation_results["valid"]:
                validation_results["message"] = "Pipeline configuration is valid"
            else:
                validation_results["message"] = "Pipeline configuration has errors"
            
            return validation_results
        except Exception as e:
            return {
                "valid": False,
                "errors": [str(e)],
                "message": "Error validating pipeline configuration"
            }
    
    def _move_to_history(self, task_id: str, task_info: Dict[str, Any]):
        """Move completed task to history."""
        try:
            # Add to history
            self._task_history.append(task_info.copy())
            
            # Remove from active tasks
            if task_id in self._active_tasks:
                del self._active_tasks[task_id]
            
            # Limit history size
            if len(self._task_history) > self._max_history_size:
                self._task_history.pop(0)
        except Exception as e:
            logger.error(f"Error moving task to history: {e}", exc_info=True)
    
    async def cleanup(self):
        """Cleanup resources."""
        try:
            if self.pipeline:
                await self.pipeline.cleanup()
            logger.info("Pipeline manager cleanup completed")
        except Exception as e:
            logger.error(f"Error during pipeline manager cleanup: {e}", exc_info=True)
